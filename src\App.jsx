import { useState, useEffect } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import SecurityScreen from './components/SecurityScreen'
import TransitionScreen from './components/TransitionScreen'
import BirthdayMain from './components/BirthdayMain'
import './styles/App.css'

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [showTransition, setShowTransition] = useState(false)
  const [showBirthday, setShowBirthday] = useState(false)
  
  useEffect(() => {
    const auth = localStorage.getItem('birthday_authenticated')
    if (auth === 'true') {
      setIsAuthenticated(true)
      setShowBirthday(true)
    }
  }, [])

  const handleAuthentication = () => {
    setIsAuthenticated(true)
    setShowTransition(true)
    localStorage.setItem('birthday_authenticated', 'true')
  }

  const handleTransitionComplete = () => {
    setShowTransition(false)
    setShowBirthday(true)
  }

  if (showTransition) {
    return <TransitionScreen onComplete={handleTransitionComplete} />
  }

  return (
    <div className="app-container">
      <Routes>
        <Route 
          path="/" 
          element={
            !isAuthenticated 
              ? <SecurityScreen onAuthenticated={handleAuthentication} /> 
              : <Navigate to="/birthday" replace />
          } 
        />
        <Route 
          path="/birthday" 
          element={
            showBirthday 
              ? <BirthdayMain /> 
              : <Navigate to="/" replace />
          } 
        />
      </Routes>
    </div>
  )
}

export default App