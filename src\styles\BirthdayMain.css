.birthday-main {
	min-height: 100vh;
	background: linear-gradient(135deg, var(--primary-50), var(--accent-50));
	padding: var(--space-4);
	overflow-x: hidden;
	position: relative;
}

.content-container {
	max-width: 1200px;
	margin: 0 auto;
	padding: var(--space-4);
	position: relative;
	z-index: 1;
}

.header-section {
	text-align: center;
	margin-bottom: var(--space-8);
	position: relative;
}

.header-section h1 {
	font-size: 4rem;
	background: linear-gradient(45deg, var(--primary-600), var(--primary-400));
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	margin-bottom: var(--space-2);
	filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.1));
}

.subtitle {
	font-size: 1.8rem;
	color: var(--primary-600);
	font-family: var(--font-heading);
}

.message-section {
	background: rgba(255, 255, 255, 0.9);
	padding: var(--space-6);
	border-radius: var(--radius-lg);
	box-shadow: var(--shadow-lg);
	margin-bottom: var(--space-8);
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.2);
}

.message-section h2 {
	color: var(--primary-600);
	margin-bottom: var(--space-4);
	font-size: 2.5rem;
}

.message-section p {
	font-size: 1.4rem;
	line-height: 1.8;
	color: var(--neutral-700);
	font-family: var(--font-heading);
}

.memories-section {
	margin-bottom: var(--space-8);
}

.memories-section h2 {
	text-align: center;
	color: var(--primary-600);
	margin-bottom: var(--space-6);
	font-size: 2.5rem;
}

.memories-stack {
	position: relative;
	padding: var(--space-4);
	perspective: 1000px;
}

.memory-card {
	background: rgba(255, 255, 255, 0.9);
	border-radius: var(--radius-lg);
	padding: var(--space-4);
	margin-bottom: var(--space-4);
	box-shadow: var(--shadow-lg);
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.2);
	transition: all 0.3s ease;
	cursor: pointer;
}

.memory-card h3 {
	color: var(--primary-600);
	margin-bottom: var(--space-3);
	font-size: 1.8rem;
	text-align: center;
}

.memory-images {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: var(--space-3);
	margin-bottom: var(--space-3);
}

.memory-images img {
	width: 100%;
	height: 200px;
	object-fit: cover;
	border-radius: var(--radius-md);
	cursor: pointer;
	transition: all 0.3s ease;
}

.memory-card p {
	color: var(--neutral-700);
	font-size: 1.2rem;
	text-align: center;
	font-family: var(--font-heading);
}

.reasons-section {
	margin-bottom: var(--space-8);
	padding: var(--space-4);
}

.reasons-title-container {
	text-align: center;
	margin-bottom: var(--space-6);
	min-height: 120px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.reasons-title {
	color: var(--primary-600);
	font-size: 2.5rem;
	margin: 0;
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: var(--space-2);
}

.crossed-out {
	position: relative;
	color: var(--neutral-400);
}

.crossed-out::after {
	content: "";
	position: absolute;
	left: 0;
	top: 50%;
	width: 100%;
	height: 3px;
	background: #ff4444;
	transform: translateY(-50%) rotate(-5deg);
	animation: drawLine 0.8s ease-out forwards;
}

@keyframes drawLine {
	from {
		width: 0;
	}
	to {
		width: 100%;
	}
}

.surprise-number {
	color: var(--primary-500);
	font-weight: bold;
	text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
	display: inline-block;
}

.surprise-text {
	font-size: 1.2rem;
	color: var(--primary-500);
	font-weight: 600;
	margin-top: var(--space-2);
	display: block;
	text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

/* Drawing Explosion Styles */
.drawing-explosion-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(10px);
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
}

.explosion-content {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
}

.explosion-sparkles {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

.sparkle {
	position: absolute;
	font-size: 1.5rem;
	pointer-events: none;
}

.drawing-container-explosion {
	background: rgba(255, 255, 255, 0.9);
	padding: var(--space-6);
	border-radius: var(--radius-lg);
	box-shadow: var(--shadow-lg);
	border: 3px solid var(--primary-200);
}

.drawing-image-explosion {
	max-width: 300px;
	max-height: 250px;
	width: auto;
	height: auto;
	border-radius: var(--radius-md);
	box-shadow: var(--shadow-md);
}

/* Settled Drawing Styles */
.drawing-container-settled {
	background: rgba(255, 255, 255, 0.9);
	padding: var(--space-4);
	border-radius: var(--radius-lg);
	box-shadow: var(--shadow-lg);
	border: 2px solid var(--primary-200);
	text-align: center;
	margin: 0 auto var(--space-6) auto;
	max-width: 400px;
	backdrop-filter: blur(10px);
}

.drawing-image-settled {
	max-width: 250px;
	max-height: 200px;
	width: auto;
	height: auto;
	border-radius: var(--radius-md);
	box-shadow: var(--shadow-sm);
}

.drawing-caption {
	margin-top: var(--space-2);
	font-size: 1.1rem;
	color: var(--primary-600);
	font-weight: 600;
	margin-bottom: 0;
}

.reasons-gallery {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: var(--space-4);
	margin-bottom: var(--space-4);
	position: relative;
	min-height: 300px;
}

.gallery-nav {
	background: rgba(255, 255, 255, 0.9);
	border: none;
	border-radius: 50%;
	width: 50px;
	height: 50px;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 1.5rem;
	color: var(--primary-600);
	cursor: pointer;
	box-shadow: var(--shadow-md);
	z-index: 2;
}

.reason-display {
	flex: 1;
	max-width: 600px;
	perspective: 1000px;
}

.reason-card-large {
	background: rgba(255, 255, 255, 0.9);
	padding: var(--space-6);
	border-radius: var(--radius-lg);
	box-shadow: var(--shadow-lg);
	position: relative;
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.2);
	min-height: 200px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.reason-number-large {
	position: absolute;
	top: var(--space-3);
	left: var(--space-3);
	background: var(--primary-500);
	color: white;
	padding: var(--space-2) var(--space-3);
	border-radius: var(--radius-full);
	font-size: 0.9rem;
}

.reason-text {
	font-size: 1.8rem;
	color: var(--primary-700);
	text-align: center;
	font-family: var(--font-heading);
	margin: 0;
	padding: var(--space-4);
}

.reasons-preview {
	text-align: center;
	color: var(--primary-600);
	font-size: 1.2rem;
	margin-top: var(--space-4);
}

.moods-section {
	margin-bottom: var(--space-8);
	padding: var(--space-4);
}

.moods-section h2 {
	text-align: center;
	color: var(--primary-600);
	margin-bottom: var(--space-6);
	font-size: 2.5rem;
}

.moods-container {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
	gap: var(--space-6);
	padding: var(--space-4);
}

.mood-card {
	background: white;
	border-radius: var(--radius-lg);
	overflow: hidden;
	box-shadow: var(--shadow-lg);
	position: relative;
	transition: all 0.3s ease;
}

.mood-card img {
	width: 100%;
	height: 300px;
	object-fit: cover;
}

.mood-content {
	padding: var(--space-4);
	text-align: center;
}

.mood-content h3 {
	color: var(--primary-600);
	margin-bottom: var(--space-2);
	font-size: 1.8rem;
}

.mood-content p {
	color: var(--neutral-700);
	font-size: 1.1rem;
}

.mood-arrow {
	position: absolute;
	top: 50%;
	right: var(--space-4);
	font-size: 2rem;
	color: var(--primary-500);
	text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.moods-footer {
	text-align: center;
	margin-top: var(--space-6);
}

.moods-footer h3 {
	font-size: 2rem;
	color: var(--primary-600);
	display: inline-block;
	padding: var(--space-4);
	background: rgba(255, 255, 255, 0.9);
	border-radius: var(--radius-lg);
	box-shadow: var(--shadow-lg);
}

.birthday-main::before {
	content: "";
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: radial-gradient(circle at 10% 20%, rgba(255, 105, 180, 0.1) 0%, transparent 20%),
		radial-gradient(circle at 90% 30%, rgba(236, 72, 153, 0.1) 0%, transparent 20%),
		radial-gradient(circle at 30% 70%, rgba(219, 39, 119, 0.1) 0%, transparent 20%),
		radial-gradient(circle at 70% 80%, rgba(190, 24, 93, 0.1) 0%, transparent 20%);
	pointer-events: none;
	z-index: 0;
}

.pswp__bg {
	background: rgba(0, 0, 0, 0.9) !important;
}

@media (max-width: 768px) {
	.header-section h1 {
		font-size: 2.5rem;
	}

	.subtitle {
		font-size: 1.4rem;
	}

	.message-section,
	.final-note {
		padding: var(--space-4);
	}

	.memory-images {
		grid-template-columns: 1fr;
	}

	.memory-images img {
		height: 250px;
	}

	.message-section p,
	.final-note p {
		font-size: 1.2rem;
	}

	.wishes-list {
		grid-template-columns: 1fr;
	}

	.reasons-gallery {
		flex-direction: column;
		gap: var(--space-2);
	}

	.gallery-nav {
		transform: rotate(90deg);
	}

	.reason-text {
		font-size: 1.4rem;
	}

	.reasons-title {
		font-size: 2rem;
	}

	.surprise-text {
		font-size: 1rem;
	}

	.reasons-title-container {
		min-height: 100px;
	}

	/* Mobile drawing styles */
	.drawing-container-settled {
		max-width: 300px;
		padding: var(--space-3);
	}

	.drawing-image-settled {
		max-width: 200px;
		max-height: 150px;
	}

	.drawing-container-explosion {
		padding: var(--space-4);
	}

	.drawing-image-explosion {
		max-width: 250px;
		max-height: 200px;
	}

	.sparkle {
		font-size: 1.2rem;
	}
}

.fade-in {
	animation: fadeIn 1s ease-in forwards;
}

.slide-up {
	animation: slideUp 0.5s ease-out forwards;
}

.bounce {
	animation: bounce 1s ease infinite;
}

@keyframes fadeIn {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

@keyframes slideUp {
	from {
		transform: translateY(50px);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}

@keyframes bounce {
	0%,
	100% {
		transform: translateY(0);
	}
	50% {
		transform: translateY(-10px);
	}
}
