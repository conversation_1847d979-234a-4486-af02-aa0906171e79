.security-screen {
  min-height: 100vh;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
  padding: var(--space-4);
}

.security-container {
  background: white;
  padding: var(--space-6);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  max-width: 500px;
  width: 100%;
  text-align: center;
}

.security-container h1 {
  color: var(--primary-600);
  margin-bottom: var(--space-2);
}

.subtitle {
  color: var(--neutral-600);
  margin-bottom: var(--space-4);
  font-size: 1.1rem;
}

.question-container {
  margin-top: var(--space-4);
}

.question {
  font-size: 1.2rem;
  color: var(--neutral-800);
  margin-bottom: var(--space-4);
}

form {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

input {
  padding: var(--space-3);
  border: 2px solid var(--primary-200);
  border-radius: var(--radius-md);
  font-size: 1rem;
  transition: all 0.3s ease;
}

input:focus {
  outline: none;
  border-color: var(--primary-400);
  box-shadow: 0 0 0 3px var(--primary-100);
}

input.error {
  border-color: var(--error-500);
  animation: shake 0.5s ease-in-out;
}

button {
  background-color: var(--primary-500);
  color: white;
  border: none;
  padding: var(--space-3);
  border-radius: var(--radius-md);
  font-size: 1rem;
  transition: all 0.3s ease;
}

button:hover {
  background-color: var(--primary-600);
}

.hint-button {
  background-color: var(--secondary-100);
  color: var(--secondary-700);
  margin-top: var(--space-2);
}

.hint-button:hover {
  background-color: var(--secondary-200);
}

.hint {
  color: var(--secondary-600);
  margin-top: var(--space-3);
  font-style: italic;
}

.error-message {
  color: var(--error-500);
  margin-top: var(--space-3);
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-10px); }
  75% { transform: translateX(10px); }
}

@media (max-width: 480px) {
  .security-container {
    padding: var(--space-4);
  }
  
  .question {
    font-size: 1.1rem;
  }
}