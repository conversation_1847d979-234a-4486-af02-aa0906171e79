{"name": "vite-react-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.22.3", "framer-motion": "^11.0.8", "react-confetti": "^6.1.0", "photoswipe": "^5.4.3", "react-photoswipe-gallery": "^3.0.1", "@floating-ui/react": "^0.26.9", "react-intersection-observer": "^9.8.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^9.9.1", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "vite": "^5.4.2"}}