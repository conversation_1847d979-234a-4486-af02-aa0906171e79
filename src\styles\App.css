.app-container {
  min-height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
}

/* Animation keyframes */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.1);
  }
  50% {
    transform: scale(1);
  }
  75% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* Utility classes */
.text-center {
  text-align: center;
}

.mb-1 {
  margin-bottom: var(--space-1);
}

.mb-2 {
  margin-bottom: var(--space-2);
}

.mb-3 {
  margin-bottom: var(--space-3);
}

.mb-4 {
  margin-bottom: var(--space-4);
}

.mb-5 {
  margin-bottom: var(--space-5);
}

.mt-1 {
  margin-top: var(--space-1);
}

.mt-2 {
  margin-top: var(--space-2);
}

.mt-3 {
  margin-top: var(--space-3);
}

.mt-4 {
  margin-top: var(--space-4);
}

.mt-5 {
  margin-top: var(--space-5);
}

.p-1 {
  padding: var(--space-1);
}

.p-2 {
  padding: var(--space-2);
}

.p-3 {
  padding: var(--space-3);
}

.p-4 {
  padding: var(--space-4);
}

.p-5 {
  padding: var(--space-5);
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.gap-1 {
  gap: var(--space-1);
}

.gap-2 {
  gap: var(--space-2);
}

.gap-3 {
  gap: var(--space-3);
}

.gap-4 {
  gap: var(--space-4);
}

.w-full {
  width: 100%;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--space-3);
  padding-right: var(--space-3);
}

/* Animation classes */
.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-fadeIn {
  animation: fadeIn 1s ease-in-out forwards;
}

.animate-slideInUp {
  animation: slideInUp 0.5s ease-out forwards;
}

.animate-heartbeat {
  animation: heartbeat 1.5s ease-in-out infinite;
}

/* Delay classes */
.delay-100 {
  animation-delay: 100ms;
}

.delay-200 {
  animation-delay: 200ms;
}

.delay-300 {
  animation-delay: 300ms;
}

.delay-400 {
  animation-delay: 400ms;
}

.delay-500 {
  animation-delay: 500ms;
}