import { motion } from 'framer-motion';
import '../styles/TransitionScreen.css';

function TransitionScreen({ onComplete }) {
  return (
    <motion.div 
      className="transition-screen"
      initial={{ opacity: 1 }}
      animate={{ opacity: 0 }}
      transition={{ duration: 2, delay: 3 }}
      onAnimationComplete={onComplete}
    >
      <motion.div 
        className="heart-container"
        animate={{
          scale: [1, 1.2, 1],
          rotate: [0, 10, -10, 0]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          repeatType: "reverse"
        }}
      >
        <motion.div 
          className="heart"
          animate={{
            backgroundColor: ['#ff69b4', '#ff1493', '#ff69b4']
          }}
          transition={{
            duration: 2,
            repeat: Infinity
          }}
        />
      </motion.div>
      
      <motion.div 
        className="loading-text"
        animate={{
          opacity: [0, 1, 0]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          repeatType: "reverse"
        }}
      >
        <h2>Preparing Your Special Surprise</h2>
        <div className="loading-dots">
          <span>.</span>
          <span>.</span>
          <span>.</span>
        </div>
      </motion.div>
    </motion.div>
  );
}

export default TransitionScreen;