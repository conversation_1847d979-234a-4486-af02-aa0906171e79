import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import '../styles/SecurityScreen.css';

const questions = [
  {
    question: "What's my favorite color?",
    answer: "pink",
    hint: "It's a sweet and romantic color 💖"
  },
  {
    question: "Where did we have our first date?",
    answer: "starbucks",
    hint: "Coffee makes memories sweeter ☕"
  },
  {
    question: "What's my favorite food that you cook?",
    answer: "pasta",
    hint: "Italian cuisine is the way to my heart 🍝"
  },
  {
    question: "What pet name do you call me?",
    answer: "baby",
    hint: "It's a sweet and common term of endearment 👶"
  },
  {
    question: "What month did we first meet?",
    answer: "march",
    hint: "Spring was just beginning 🌸"
  }
];

function SecurityScreen({ onAuthenticated }) {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answer, setAnswer] = useState('');
  const [showHint, setShowHint] = useState(false);
  const [showError, setShowError] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (answer.toLowerCase().trim() === questions[currentQuestion].answer) {
      setShowError(false);
      if (currentQuestion === questions.length - 1) {
        onAuthenticated();
      } else {
        setCurrentQuestion(prev => prev + 1);
        setAnswer('');
        setShowHint(false);
      }
    } else {
      setShowError(true);
      setTimeout(() => setShowError(false), 2000);
    }
  };

  return (
    <motion.div 
      className="security-screen"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <motion.div 
        className="security-container"
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <h1>🔐 Security Check</h1>
        <p className="subtitle">Prove you're my one and only! 💕</p>
        
        <motion.div 
          className="question-container"
          key={currentQuestion}
          initial={{ x: 100, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          exit={{ x: -100, opacity: 0 }}
        >
          <h2>Question {currentQuestion + 1}/{questions.length}</h2>
          <p className="question">{questions[currentQuestion].question}</p>
          
          <form onSubmit={handleSubmit}>
            <motion.input
              type="text"
              value={answer}
              onChange={(e) => setAnswer(e.target.value)}
              className={showError ? 'error' : ''}
              whileFocus={{ scale: 1.05 }}
              transition={{ type: 'spring', stiffness: 300 }}
            />
            <motion.button
              type="submit"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Submit
            </motion.button>
          </form>
          
          <motion.button
            className="hint-button"
            onClick={() => setShowHint(!showHint)}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Need a hint? 💭
          </motion.button>
          
          <AnimatePresence>
            {showHint && (
              <motion.p
                className="hint"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
              >
                {questions[currentQuestion].hint}
              </motion.p>
            )}
            
            {showError && (
              <motion.p
                className="error-message"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
              >
                Oops! That's not quite right. Try again, sweetie! 💝
              </motion.p>
            )}
          </AnimatePresence>
        </motion.div>
      </motion.div>
    </motion.div>
  );
}

export default SecurityScreen;