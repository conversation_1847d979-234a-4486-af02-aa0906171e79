.transition-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-50), var(--accent-50));
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.heart-container {
  position: relative;
  width: 150px;
  height: 150px;
  margin-bottom: 2rem;
}

.heart {
  position: absolute;
  width: 100%;
  height: 100%;
  background: var(--primary-500);
  clip-path: path('M75,150 a37.5,37.5 0 0,1 -75,-37.5 a37.5,37.5 0 0,1 75,-37.5 a37.5,37.5 0 0,1 75,37.5 a37.5,37.5 0 0,1 -75,37.5z');
  box-shadow: 0 0 30px rgba(236, 72, 153, 0.5);
}

.loading-text {
  text-align: center;
}

.loading-text h2 {
  font-family: var(--font-heading);
  font-size: 2rem;
  color: var(--primary-700);
  margin-bottom: 1rem;
}

.loading-dots {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
}

.loading-dots span {
  font-size: 2rem;
  color: var(--primary-600);
  animation: bounce 0.5s ease infinite;
}

.loading-dots span:nth-child(2) {
  animation-delay: 0.1s;
}

.loading-dots span:nth-child(3) {
  animation-delay: 0.2s;
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}